from weboffice_v2.pc.component.actions.base import BaseAction
import requests
import re


class CustomAction(BaseAction):
    """
    选中列 自定义动作
    """

    def parse_args(self, args):
        # 解析动作参数。在这里可以添加额外的或者修改动作参数
        self.add_required_arg('browser_selector','BrowserSelector')
        self.add_required_arg('component','Component')
        self.add_required_arg('column_number','ColumnNumber')

    def execute(self):
        component = self.get_arg('component')
        browser_selector = self.get_arg('browser_selector')
        column_number = self.get_arg('column_number')
        self.select_column(browser_selector,component,column_number)
        

    def select_column(self,browser_selector,component,column_number):
        if component == 'ET' or component == 'AS':
            js = f"async()=>await window.WPSOpenApi.Application.Range('{column_number}').EntireColumn.Select()"
            
        elif component == 'DB':
            column_number = self.extract_column_letters(column_number)
            column_number = self.excel_column_to_number(column_number)
            js = f"async()=>await window.WPSOpenApi.Application.ActiveDBSheet.Selection.SelectCol({column_number})"
            
        self.execute_script(browser_selector,js)
        
    def extract_column_letters(self, column_number):
        match = re.match(r'^([A-Za-z]+)', column_number)
        if match:
            return match.group(1).upper()
        else:
            raise ValueError(f"无效的单元格引用格式: {column_number}")

    def excel_column_to_number(self, column_number)->int:
        num = 0
        for char in column_number.upper():
            num = num * 26 + (ord(char) - ord('A') + 1)
        return num

    def execute_script(self,browser_selector,script):
        args = {
            'BrowserSelector': browser_selector,
            'ScriptText': script
        }
        return self.driver_context.driver.execute_action(action_name='EvaluateJS',args=args)

if __name__ == '__main__':
    pass






