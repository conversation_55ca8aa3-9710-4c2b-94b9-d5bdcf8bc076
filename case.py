# -- coding: utf-8 --
# @Time : 2024-9-23 14:35:51
# <AUTHOR> lirunfang
import sys
import KatyushaDriver
import inspect

args = sys.argv
driver = KatyushaDriver.Katyusha(args)
driver.stage('First', 'First')
driver.execute_action(
    action_name='LaunchBrowser',
    desc='打开浏览器',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserType': 'Chrome',
            'IsSetMax': True,
            'DeviceType': '',
            'BrowserName': '',
            'ConnectionOptions': '',
            'ExecutablePath': '',
        },
    },
)

driver.execute_action(
    action_name='AddCookies',
    desc='添加Cookie',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0', 
            'Cookies': [{'name': 'weboffice_branch', 'value': 'kdocs-amd-t-release-minio', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}]
            },
    },
)
driver.execute_action(
    action_name='GoTo',
    desc='打开页面',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 300,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0',
            # 'Url': 'https://www.kdocs.cn/l/cpn4qOPu6BTu', #ET
            # 'Url': 'https://www.kdocs.cn/l/cdGmuyqvBkr2', #AS
            'Url': 'https://www.kdocs.cn/l/ckx2HeM3unA9?R=L1MvNQ==',    #DB
            # 'Url': 'https://www.kdocs.cn/l/ccrh96NsLecD', #AP
            # 'Url': 'https://www.kdocs.cn/l/crwRhAjXnn6p?from=docs', #WPP
            # 'Url': 'https://www.kdocs.cn/l/cdlfICUCvI6A', #WPS
            'WaitUntil': 'load',
            'Referer': 'none',
        },
    },
)

driver.execute_action(
    action_name='CustomAction',
    desc='选中行',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0',
            'customActionType': 'CUSTOM',
            'customActionIdxId': 'select_column',
            'InstanceIndex': '0',
            'BrowserIndex': 1,
            'BrowserType': '{{DefaultBrowserType}}',
            'ColumnNumber': 'B3',
            'Component': 'DB',
        },
    },
)



# driver.execute_action(
#     action_name='EvaluateJS',
#     desc='执行脚本片段',
#     app_type='WO_PC_V2',
#     is_must_execute=False,
#     is_must_pass=True,
#     args={
#         'Timeout': 60,
#         'Descr': '',
#         'Arg': {
#             'BrowserSelector': '0.0.0',
#             'ScriptText': 'window.g_rpt.commandProc.sendCommand({name:"shapeInsert",param:{     "category": "slides",     "layerType": 65538,     "slideIdx": 0,     "slideId": 150995203,     "shapePath": [         3     ],     "shapeid": -22937598,     "slideProps": {         "insertShape": {             "group": 2,             "item": 25,             "rect": {                 "x": 5640730.348351884,                 "y": 771748.5819777398,                 "width": 1044130.5,                 "height": 1044130.5,                 "rotation": 0,                 "horizontalFlipped": false,                 "verticalFlipped": false             },             "bCopyDefProps": true,             "bDefCreate": true,             "modifyProps": {                 "cmdExtends": {                     "actionType": 7                 },                 "fill": {                     "type": 2,                     "color": {                         "type": 0,                         "sRgb": 14870253,                         "isAutoColor": false,                         "transOp": [                             {                                 "op": 0,                                 "val": 1                             }                         ]                     }                 },                 "modifiers": [                     {                         "tag": "fill",                         "val": {                             "type": 2,                             "color": {                                 "type": 0,                                 "sRgb": 11449792,                                 "isAutoColor": false,                                 "transOp": [                                     {                                         "op": 0,                                         "val": 1                                     }                                 ]                             }                         }                     },                     {                         "tag": "color",                         "val": "#000000"                     }                 ],                 "cmdFlags": {},                 "shapeType": 2,                 "textFont": {                     "textColor": {                         "color": {                             "type": 0,                             "isAutoColor": false,                             "toRGB": "#00000000",                             "sRgb": 0                         },                         "type": 2                     }                 },                 "outline": {                     "fill": {                         "type": 2,                         "color": {                             "type": 0,                             "sRgb": 11449792,                             "isAutoColor": false,                             "transOp": [                                 {                                     "op": 0,                                     "val": 1                                 }                             ]                         }                     },                     "dashType": 0,                     "joinType": 0,                     "miterLimit": 8,                     "width": 12700,                     "endingCap": 0,                     "compoundLine": 0,                     "alignment": 0                 },                 "textFrameProps": {                     "anchorType": 1,                     "autoFit": 0,                     "columnRightToLeft": 0                 },                 "rect": {                     "left": 8883.039918664384,                     "top": 1215.3520976027398,                     "right": 10527.339918664384,                     "bottom": 2859.6520976027396                 },                 "transform2DInfo": {                     "flipRotation": {                         "rotation": 0,                         "horizontalFlipped": false,                         "verticalFlipped": false                     }                 }             }         },         "cmdName": "insertShape",         "localId": -22937598     },     "isLocalExec": false,     "userData": {         "_k": "197bf809d5a",         "CMDVER": 1,         "cmdToken": "197bf809d4d"     },     "slideIndex": 0,     "token": "197bf809d4d",     "mainCommand": "",     "actionName": "",     "extArgs": null }})',
#             'ScriptFile': '',
#             'Arguments': '',
#         },
#     },
# )

# driver.execute_action(
#     action_name='CustomAction',
#     desc='等待内核数据计算完成',
#     app_type='WO_PC_V2',
#     is_must_execute=False,
#     is_must_pass=True,
#     args={
#         'Timeout': 60,
#         'Descr': '',
#         'Arg': {
#             'BrowserSelector': '0.0.0',
#             'customActionType': 'CUSTOM',
#             'customActionIdxId': 'wait_kernel_loadend',
#             'InstanceIndex': '0',
#             'BrowserIndex': 1,
#             'BrowserType': '{{DefaultBrowserType}}',
#         },
#     },
# )

# driver.execute_checkpoint(
#     checkpoint_name='CustomAction',
#     desc='控制台无报错',
#     app_type='WO_PC_V2',
#     is_must_pass=True,
#     args={
#         'Timeout': 60,
#         'Descr': '',
#         'Expected': '2',
#         'Arg': {
#             'BrowserSelector': '0.0.0',
#             'customActionType': 'CHECKPOINT',
#             'customActionIdxId': 'check_console_error',
#             'InstanceIndex': '0',
#             'BrowserIndex': 1,
#             'BrowserType': '{{DefaultBrowserType}}',
#         },
#     },
# )

driver.execute_action(
    action_name='WaitTimeout',
    desc='延时等待',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'Duration': 10}},
)


driver.execute_action(
    action_name='QuitBrowser',
    desc='关闭浏览器',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'BrowserSelector': '*'}},
)
driver.quit()

